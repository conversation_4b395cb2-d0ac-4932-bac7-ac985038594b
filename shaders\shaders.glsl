//------------------------------------------------------------------------------
// Shaders for Offscreen Rendering
// Compile with: sokol-shdc --input shaders.glsl --output shaders.glsl.h --slang glsl430:hlsl5:metal_macos
//------------------------------------------------------------------------------

//==============================================================================
// OFFSCREEN PASS SHADERS (Render 3D scene)
//==============================================================================

@vs offscreen
layout(binding=0) uniform vs_params {
    mat4 mvp;
};

in vec2 position;
in vec4 color0;

out vec4 color;

void main() {
    gl_Position = mvp * vec4(position, 0.0, 1.0);
    color = color0;
}
@end

@fs offscreen_fs
in vec4 color;
out vec4 frag_color;

void main() {
    frag_color = color;
}
@end

@program offscreen offscreen offscreen_fs

//==============================================================================
// DISPLAY PASS SHADERS (Show offscreen texture on screen)
//==============================================================================

@vs display
in vec2 pos;
in vec2 uv;
out vec2 tex_coord;

void main() {
    gl_Position = vec4(pos, 0.0, 1.0);
    tex_coord = uv;
}
@end

@fs display_fs
// Note: textures and storage images now share the same binding space
layout(binding=0) uniform texture2D tex;
layout(binding=0) uniform sampler smp;

in vec2 tex_coord;
out vec4 frag_color;

void main() {
    // Sample the offscreen rendered texture
    frag_color = texture(sampler2D(tex, smp), tex_coord);
}
@end

@program display display display_fs