#include <sokol/sokol_gfx.h>
#include <sokol/sokol_app.h>
#include <sokol/sokol_log.h>
#include <sokol/sokol_glue.h>

#include "shaders.glsl.h"

#define OFFSCREEN_WIDTH 320
#define OFFSCREEN_HEIGHT 180

typedef struct
{
    float x, y;
    float u, v;
    uint32_t color;
} vertex_t;

static struct
{
    // Offscreen pass resources
    struct
    {
        sg_image color_img;
        sg_image depth_img;
        sg_view color_att;   // view for rendering to color attachment
        sg_view depth_att;   // view for rendering to depth-stencil attachment
        sg_view color_tex;   // view for sampling color attachment as texture
        sg_pipeline pip;
        sg_bindings bind;
        sg_pass_action pass_action;
    } offscreen;

    // Display pass resources
    struct
    {
        sg_pipeline pip;
        sg_bindings bind;
        sg_pass_action pass_action;
        sg_sampler sampler;
    } display;
} state;

static void init(void)
{
    sg_setup(&(sg_desc){
        .environment = sglue_environment(),
        .logger.func = slog_func,
    });

    // Initialize offscreen pass resources
    {
        // Create offscreen render target image
        state.offscreen.color_img = sg_make_image(&(sg_image_desc){
            .usage = { .color_attachment = true },
            .width = OFFSCREEN_WIDTH,
            .height = OFFSCREEN_HEIGHT,
            .pixel_format = SG_PIXELFORMAT_RGBA8,
            .sample_count = 1,
            .label = "offscreen-color"
        });

        // Create depth-stencil image
        state.offscreen.depth_img = sg_make_image(&(sg_image_desc){
            .usage = { .depth_stencil_attachment = true },
            .width = OFFSCREEN_WIDTH,
            .height = OFFSCREEN_HEIGHT,
            .pixel_format = SG_PIXELFORMAT_DEPTH,
            .sample_count = 1,
            .label = "offscreen-depth"
        });

        // Create attachment views
        state.offscreen.color_att = sg_make_view(&(sg_view_desc){
            .color_attachment = { .image = state.offscreen.color_img },
            .label = "offscreen-color-attachment",
        });

        state.offscreen.depth_att = sg_make_view(&(sg_view_desc){
            .depth_stencil_attachment = { .image = state.offscreen.depth_img },
            .label = "offscreen-depth-attachment",
        });

        state.offscreen.color_tex = sg_make_view(&(sg_view_desc){
            .texture = { .image = state.offscreen.color_img },
            .label = "offscreen-color-texture",
        });

        // Create pipeline for offscreen rendering
        state.offscreen.pip = sg_make_pipeline(&(sg_pipeline_desc){
            .shader = sg_make_shader(offscreen_shader_desc(sg_query_backend())),
            .colors[0].pixel_format = SG_PIXELFORMAT_RGBA8,
            .layout = {
                .attrs = {
                    [ATTR_offscreen_position].format = SG_VERTEXFORMAT_FLOAT2,
                    [ATTR_offscreen_color0].format = SG_VERTEXFORMAT_UBYTE4N
                }
            },
            .index_type = SG_INDEXTYPE_UINT16,
            .cull_mode = SG_CULLMODE_BACK,
            .depth = {
                .compare = SG_COMPAREFUNC_LESS_EQUAL,
                .write_enabled = true,
                .pixel_format = SG_PIXELFORMAT_DEPTH,
            },
            .label = "offscreen-pipeline"
        });

        vertex_t vertices[] = {
            {-1.0f, -1.0f, 0.0f, 0.0f, 0xFF0000FF},
            { 1.0f, -1.0f, 1.0f, 0.0f, 0xFF0000FF},
            { 1.0f,  1.0f, 1.0f, 1.0f, 0xFF0000FF},
            {-1.0f,  1.0f, 0.0f, 1.0f, 0xFF0000FF}
        };
        uint16_t indices[] = {0, 1, 2, 0, 2, 3};

        state.offscreen.bind.vertex_buffers[0] = sg_make_buffer(&(sg_buffer_desc){
            .data = SG_RANGE(vertices),
            .label = "offscreen-vertices",
        });

        state.offscreen.bind.index_buffer = sg_make_buffer(&(sg_buffer_desc){
            .data = SG_RANGE(indices),
            .label = "offscreen-indices",
        });

        state.offscreen.pass_action = (sg_pass_action){
            .colors[0] = {
                .load_action = SG_LOADACTION_CLEAR,
                .clear_value = { 0.333f, 0.677f, 1.0f, 1.0f }
            }
        };
    }

    // Initialize display pass resources
    {
        state.display.pip = sg_make_pipeline(&(sg_pipeline_desc){
            .shader = sg_make_shader(display_shader_desc(sg_query_backend())),
            .layout = {
                .attrs = {
                    [ATTR_display_pos].format = SG_VERTEXFORMAT_FLOAT2,
                    [ATTR_display_uv].format = SG_VERTEXFORMAT_FLOAT2,
                }
            },
            .primitive_type = SG_PRIMITIVETYPE_TRIANGLE_STRIP,
            .label = "display-pipeline"
        });

        float quad_vertices[] = {
            // pos         uv
            -1.0f, -1.0f,  0.0f, 1.0f,  // bottom-left
             1.0f, -1.0f,  1.0f, 1.0f,  // bottom-right
            -1.0f,  1.0f,  0.0f, 0.0f,  // top-left
             1.0f,  1.0f,  1.0f, 0.0f,  // top-right
        };

        state.display.bind.vertex_buffers[0] = sg_make_buffer(&(sg_buffer_desc){
            .data = SG_RANGE(quad_vertices),
            .label = "display-vertices",
        });

        state.display.bind.views[VIEW_tex] = state.offscreen.color_tex;

        state.display.sampler = sg_make_sampler(&(sg_sampler_desc){
            .min_filter = SG_FILTER_NEAREST,
            .mag_filter = SG_FILTER_NEAREST,
            .wrap_u = SG_WRAP_CLAMP_TO_EDGE,
            .wrap_v = SG_WRAP_CLAMP_TO_EDGE,
            .label = "display-sampler",
        });

        state.display.bind.samplers[SMP_smp] = state.display.sampler;

        state.display.pass_action = (sg_pass_action){
            .colors[0] = {
                .load_action = SG_LOADACTION_CLEAR,
                .clear_value = { 0.333f, 0.677f, 1.0f, 1.0f }
            }
        };
    }
}

static void frame(void)
{
    // First pass: render game content to offscreen target
    sg_begin_pass(&(sg_pass){
        .action = state.offscreen.pass_action,
        .attachments = {
            .colors[0] = state.offscreen.color_att,
            .depth_stencil = state.offscreen.depth_att,
        },
        .label = "offscreen-pass",
    });
    sg_apply_pipeline(state.offscreen.pip);
    sg_apply_bindings(&state.offscreen.bind);

    sg_draw(0, 3, 1);
    sg_end_pass();

    // Second pass: blit offscreen target to main display with pixel-perfect scaling
    sg_begin_pass(&(sg_pass){
        .action = state.display.pass_action,
        .swapchain = sglue_swapchain(),
        .label = "display-pass",
    });
    sg_apply_pipeline(state.display.pip);
    sg_apply_bindings(&state.display.bind);

    sg_draw(0, 4, 1);
    sg_end_pass();

    sg_commit();
}

static void cleanup(void)
{
}

static void event(const sapp_event* e)
{
}

static sapp_desc
sokol_main(int argc, char* argv[])
{
    (void)argc;
    (void)argv;

    sapp_desc result = {
        .init_cb = init,
        .frame_cb = frame,
        .cleanup_cb = cleanup,
        .event_cb = event,
        .width = 1280,
        .height = 720,
        .window_title = "The Necronmancer's Cube",
        .icon.sokol_default = true,
        .logger.func = slog_func,
    };

    return result;
}