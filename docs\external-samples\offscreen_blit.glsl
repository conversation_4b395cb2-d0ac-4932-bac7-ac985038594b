@vs vs_blit

const vec2 positions[6] = {
    vec2(-1.0, -1.0),
    vec2(-1.0, 1.0),
    vec2(1.0, -1.0),
    vec2(1.0, -1.0),
    vec2(-1.0, 1.0),
    vec2(1.0, 1.0),
};

const vec2 texcoords[6] = {
    vec2(0.0, 1.0),
    vec2(0.0, 0.0),
    vec2(1.0, 1.0),
    vec2(1.0, 1.0),
    vec2(0.0, 0.0),
    vec2(1.0, 0.0),
};

out vec2 uv;

void main() {
    gl_Position = vec4(positions[gl_VertexIndex], 0.0, 1.0);
    uv = texcoords[gl_VertexIndex];
}
@end

@fs fs_blit
layout(binding=0) uniform texture2D offscreen_texture;
layout(binding=0) uniform sampler offscreen_sampler;

in vec2 uv;
out vec4 frag_color;

void main() {
    frag_color = texture(sampler2D(offscreen_texture, offscreen_sampler), uv);
}
@end

@program offscreen_blit vs_blit fs_blit
