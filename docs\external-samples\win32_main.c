#include <sokol/sokol_gfx.h>
#include <sokol/sokol_app.h>
#include <sokol/sokol_log.h>
#include <sokol/sokol_glue.h>

#include "game_triangle.glsl.h"
#include "offscreen_blit.glsl.h"

#define OFFSCREEN_WIDTH 640
#define OFFSCREEN_HEIGHT 320

static struct {
    struct {
        sg_attachments attachments;
        sg_pass_action pass_action;
        sg_pipeline pip;
        sg_bindings bind;
    } offscreen;
    struct {
        sg_pass_action pass_action;
        sg_pipeline pip;
        sg_bindings bind;
    } display;
    sg_sampler sampler;
    int scale;
} state;


static void init(void) {
    sg_setup(&(sg_desc){
        .environment = sglue_environment(),
        .logger.func = slog_func,
    });

    // Calculate integer scale factor for pixel-perfect scaling
    int window_width = sapp_width();
    int window_height = sapp_height();
    int scale_x = window_width / OFFSCREEN_WIDTH;
    int scale_y = window_height / OFFSCREEN_HEIGHT;
    state.scale = (scale_x < scale_y) ? scale_x : scale_y;
    if (state.scale < 1) state.scale = 1;

    // =============================================================================
    // OFFSCREEN SETUP - Create render target and game rendering resources
    // =============================================================================

    // Create offscreen render target image
    sg_image color_img = sg_make_image(&(sg_image_desc){
        .usage = { .color_attachment = true },
        .width = OFFSCREEN_WIDTH,
        .height = OFFSCREEN_HEIGHT,
        .pixel_format = SG_PIXELFORMAT_RGBA8,
        .sample_count = 1,
        .label = "offscreen-color-image"
    });

    // Create attachment view
    sg_view color_view = sg_make_view(&(sg_view_desc){
        .color_attachment = { .image = color_img },
        .label = "color-attachment",
    });

    // Setup offscreen attachments
    state.offscreen.attachments = (sg_attachments){
        .colors[0] = color_view,
    };

    // Setup offscreen pass action
    state.offscreen.pass_action = (sg_pass_action){
        .colors[0] = {
            .load_action = SG_LOADACTION_CLEAR,
            .clear_value = { 1.0f, 0.0f, 0.0f, 1.0f }
        }
    };

    // Setup game rendering pipeline (renders to offscreen target)
    state.offscreen.pip = sg_make_pipeline(&(sg_pipeline_desc){
        .shader = sg_make_shader(triangle_shader_desc(sg_query_backend())),
        .colors[0] = {
            .pixel_format = SG_PIXELFORMAT_RGBA8,
        },
        .sample_count = 1,
        .label = "offscreen-pipeline"
    });

    // =============================================================================
    // DISPLAY SETUP - Create resources for blitting offscreen to main display
    // =============================================================================

    // Create sampler for offscreen texture
    state.sampler = sg_make_sampler(&(sg_sampler_desc){
        .min_filter = SG_FILTER_NEAREST,
        .mag_filter = SG_FILTER_NEAREST,
        .wrap_u = SG_WRAP_CLAMP_TO_EDGE,
        .wrap_v = SG_WRAP_CLAMP_TO_EDGE,
        .label = "offscreen-sampler"
    });

    // Setup display pipeline for blitting offscreen to screen
    state.display.pip = sg_make_pipeline(&(sg_pipeline_desc){
        .shader = sg_make_shader(offscreen_blit_shader_desc(sg_query_backend())),
        .label = "display-pipeline"
    });

    // Create texture view for sampling
    sg_view texture_view = sg_make_view(&(sg_view_desc){
        .texture = { .image = color_img },
        .label = "texture-view",
    });

    // Setup bindings for display pass
    state.display.bind = (sg_bindings){
        .views[VIEW_offscreen_texture] = texture_view,
        .samplers[SMP_offscreen_sampler] = state.sampler,
    };

    // Display pass action (clear to blue)
    state.display.pass_action = (sg_pass_action){
        .colors[0] = {
            .load_action = SG_LOADACTION_CLEAR,
            .clear_value = { 0.333f, 0.677f, 1.0f, 1.0f },
        },
    };
}

static void frame(void) {
    // First pass: render game content to offscreen target
    sg_begin_pass(&(sg_pass){
        .action = state.offscreen.pass_action,
        .attachments = state.offscreen.attachments,
        .label = "offscreen-pass"
    });
    sg_apply_pipeline(state.offscreen.pip);
    sg_draw(0, 3, 1);
    sg_end_pass();

    // Second pass: blit offscreen target to main display with pixel-perfect scaling
    sg_begin_pass(&(sg_pass){
        .action = state.display.pass_action,
        .swapchain = sglue_swapchain(),
        .label = "display-pass"
    });

    // Calculate viewport for centered, integer-scaled rendering
    int window_width = sapp_width();
    int window_height = sapp_height();
    int scaled_width = OFFSCREEN_WIDTH * state.scale;
    int scaled_height = OFFSCREEN_HEIGHT * state.scale;
    int viewport_x = (window_width - scaled_width) / 2;
    int viewport_y = (window_height - scaled_height) / 2;

    sg_apply_viewport(viewport_x, viewport_y, scaled_width, scaled_height, false);
    sg_apply_pipeline(state.display.pip);
    sg_apply_bindings(&state.display.bind);
    sg_draw(0, 6, 1);

    sg_end_pass();
    sg_commit();
}

static void event(const sapp_event* e) {
    if (e->type == SAPP_EVENTTYPE_RESIZED) {
        // Recalculate scale factor when window is resized
        int window_width = sapp_width();
        int window_height = sapp_height();
        int scale_x = window_width / OFFSCREEN_WIDTH;
        int scale_y = window_height / OFFSCREEN_HEIGHT;
        state.scale = (scale_x < scale_y) ? scale_x : scale_y;
        if (state.scale < 1) state.scale = 1;
    }
}

static void cleanup(void) {
    sg_shutdown();
}

sapp_desc sokol_main(int argc, char* argv[]) {
    (void)argc; (void)argv;

    return (sapp_desc){
        .init_cb = init,
        .frame_cb = frame,
        .cleanup_cb = cleanup,
        .event_cb = event,
        .width = 1280,
        .height = 720,
        .window_title = "The Necronmancer's Cube - Pixel Perfect Renderer",
        .icon.sokol_default = true,
        .logger.func = slog_func,
    };
}