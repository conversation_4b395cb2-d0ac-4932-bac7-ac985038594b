@vs vs

const vec2 positions[3] = {
    vec2(0.0, 0.5),
    vec2(-0.5, -0.5),
    vec2(0.5, -0.5),
};

const vec4 colors[3] = {
    vec4(1.0, 0.0, 0.0, 1.0),  // Red
    vec4(0.0, 1.0, 0.0, 1.0),  // Green
    vec4(0.0, 0.0, 1.0, 1.0),  // Blue
};

out vec4 color;

void main() {
    gl_Position = vec4(positions[gl_VertexIndex], 0.0, 1.0);
    color = colors[gl_VertexIndex];
}
@end

@fs fs
in vec4 color;
out vec4 frag_color;

void main() {
    frag_color = color;
}
@end

@program triangle vs fs