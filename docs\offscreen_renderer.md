# Pixel-Perfect Offscreen Renderer

## Overview

The game now uses a pixel-perfect offscreen renderer that renders all game content at a fixed resolution of 640x320 pixels, then scales it up by integer amounts to fit the display window.

## How It Works

### Two-Pass Rendering

1. **Game Pass**: Renders all game content to an offscreen render target at 640x320 resolution
2. **Display Pass**: Renders the offscreen target to the main display, scaled by integer amounts

### Key Components

#### Offscreen Render Target
- **Resolution**: 640x320 pixels (fixed)
- **Format**: RGBA8
- **Usage**: Color attachment for rendering, texture for sampling

#### Scaling Logic
- Calculates the largest integer scale factor that fits within the window
- Centers the scaled image in the window
- Maintains pixel-perfect appearance (no blurring)

#### Shaders
- **Game Shader**: `triangle2.glsl` - renders game content
- **Blit Shader**: `offscreen_blit.glsl` - samples offscreen texture and renders to screen

## Implementation Details

### State Structure
```c
static struct {
    struct {
        sg_pass_action pass_action;
        sg_pipeline pip;
        sg_bindings bind;
    } game;
    struct {
        sg_attachments attachments;
        sg_pass_action pass_action;
        sg_pipeline pip;
        sg_bindings bind;
        sg_image color_img;
        sg_view color_view;
        sg_view texture_view;
        sg_sampler sampler;
        int scale;
    } offscreen;
} state;
```

### Rendering Flow
1. Begin offscreen pass with game render target
2. Render game content (triangle, sprites, etc.)
3. End offscreen pass
4. Begin main display pass
5. Set viewport for centered, integer-scaled rendering
6. Apply blit pipeline and render fullscreen quad with offscreen texture
7. End display pass and commit

### Window Resize Handling
- Recalculates scale factor when window is resized
- Maintains integer scaling for pixel-perfect appearance
- Centers the game view in the window

## Benefits

1. **Pixel Perfect**: No sub-pixel rendering or blurring
2. **Consistent**: Game always renders at the same resolution regardless of window size
3. **Retro Aesthetic**: Perfect for pixel art games
4. **Performance**: Fixed resolution means consistent performance
5. **Scalable**: Works on any display size with integer scaling

## Usage

All game rendering should be done during the game pass. The offscreen renderer automatically handles:
- Scaling to fit the window
- Centering the image
- Maintaining aspect ratio
- Pixel-perfect rendering

The current implementation renders a white triangle as a test, but this can be replaced with any game content.
